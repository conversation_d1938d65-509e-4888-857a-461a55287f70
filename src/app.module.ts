import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MoralisModule } from './moralis/moralis.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import configuration from 'config/configuration';
import { <PERSON>rali } from './moralis/entities/morali.entity';
import { Network } from './networks/entities/network.entity';
import { QueueModule } from './queue/queue.module';
import { QuicknodeModule } from './quicknode/quicknode.module';
import { TransactionRouterModule } from './transaction-router/transaction-router.module';
import { NetworksModule } from './networks/networks.module';
import { Transaction } from './transaction-router/entities/transactions.entity';
import { WebhooksModule } from './webhooks/webhooks.module';
import { UsersModule } from './users/users.module';
import { User } from './users/entities/user.entity';
import { ApiKeyModule } from './api-key/api-key.module';
import { ApiKey } from './api-key/entities/api-key.entity';
import { Webhook } from './webhooks/entities/webhook.entity';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: configuration().database.host,
      port: configuration().database.port,
      username: configuration().database.username,
      password: configuration().database.password,
      database: configuration().database.database,
      schema: configuration().database.schema,
      
      entities: [
        Morali,
        Network,
        Transaction,
        User,
        ApiKey,
        Webhook,
      ],
      synchronize: configuration().database.synchronize === 'true' ? true : false,
      dropSchema: false,  
      // logging: true,    
    }),
    MoralisModule,
    QueueModule,
    QuicknodeModule,
    TransactionRouterModule,
    NetworksModule,
    WebhooksModule,
    UsersModule,
    ApiKeyModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
